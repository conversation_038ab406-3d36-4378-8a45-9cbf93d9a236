services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: team-balancer-backend-dev
    ports:
      - "5050:5050"
    environment:
      - PYTHONPATH=/app/src
      - PYTHONUNBUFFERED=1
      - FLASK_APP=src/app.py
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - SPREADSHEET_ID=${SPREADSHEET_ID}
      - DB_PATH=data/database.sqlite
      - DIGEST_PATH=data/digest
    volumes:
      - ./backend/src:/app/src
      - ./backend/scripts:/app/scripts
      - ./backend/data:/app/data
      - ./backend/tests:/app/tests
    command: flask run --port=5050 --debug --host=0.0.0.0

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: team-balancer-frontend-dev
    ports:
      - "5173:5173"
    depends_on:
      - backend
    environment:
      - VITE_API_BASE_URL=http://localhost:5050/api
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
      - VITE_BUILD_TAG=${BUILD_TAG:-dev}
    volumes:
      - ./frontend:/app
      - /app/node_modules
    # Explicitly set the command to ensure it runs correctly
    command: npm run dev

networks:
  default:
    name: team-balancer-network-dev
