# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.venv/
.env/
env.bak/
venv.bak/

# Environment variables and secrets
.env
.env.local
.env.development
.env.test
.env.production
*.pem
*.key

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.project
.pydevproject
.settings/
*.sublime-workspace
*.sublime-project
*.code-workspace

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Docker
Dockerfile
.dockerignore

# Git
.git
.gitignore

# Data
data/

