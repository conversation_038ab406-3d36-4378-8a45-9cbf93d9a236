.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.modal-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
}

.modal-close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.modal-close-button:hover {
  background-color: #f0f0f0;
  color: #333;
}

.modal-content {
  padding: 20px;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Button styles for modal */
.modal-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
}

.modal-button-primary {
  background-color: #673AB7;
  color: white;
}

.modal-button-primary:hover {
  background-color: #5e35b1;
}

.modal-button-secondary {
  background-color: #e0e0e0;
  color: #333;
}

.modal-button-secondary:hover {
  background-color: #d5d5d5;
}

/* Form elements inside modal */
.modal-form-group {
  margin-bottom: 16px;
}

.modal-form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.modal-form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.modal-form-group input:focus {
  border-color: #673AB7;
  outline: none;
  box-shadow: 0 0 0 2px rgba(103, 58, 183, 0.2);
}

.modal-form-group input.input-error {
  border-color: #F44336;
  background-color: rgba(244, 67, 54, 0.05);
}

.modal-form-group input.input-error:focus {
  border-color: #F44336;
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
}

/* Developer contacts section */
.developer-contacts {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  font-size: 14px;
  color: #666;
}

.developer-contacts-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.contact-message {
  margin-top: 10px;
  font-style: italic;
  color: #673AB7;
}
