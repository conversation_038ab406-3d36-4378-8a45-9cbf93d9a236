# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
*.pem
*.key

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.project
.pydevproject
.settings/
*.sublime-workspace
*.sublime-project
*.code-workspace

# Docker
.docker/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Dependency directories
node_modules/

# Build outputs
dist/
build/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
.venv/
.env/
env.bak/
venv.bak/
.pytest_cache/
.coverage
htmlcov/
.tox/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Misc
.cache/
.temp/

# Devcontainer
.devcontainer/
